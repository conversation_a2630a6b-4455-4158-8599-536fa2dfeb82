/**
 * ============================================================================
 * 🚀 核心业务流程 - Gemini API调用器 (子层实现)
 * ============================================================================
 *
 * @fileoverview Gemini API调用器 - 子层实现
 * @description 负责Gemini API调用的具体实现，处理远程AI服务
 * 
 * @businessFlow Gemini API调用
 * 在核心业务流程中的位置：
 * 输入内容 → 本地渠道特征检测 → 提示词组合
 *     ↓
 * 【当前文件职责】发送Gemini API - 远程处理
 *     ↓
 * Gemini返回解析结果 - 远程处理
 *     ↓
 * 本地结果处理 (result-processor.js) → 订单管理
 *
 * @architecture Child Layer (子层) - 远程处理实现
 * - 职责：Gemini API调用的具体实现
 * - 原则：专注单一功能，不依赖其他子层
 * - 接口：为母层提供API调用服务
 *
 * @dependencies 依赖关系
 * 上游依赖：
 * - controllers/business-flow-controller.js (母层控制器调用)
 * 下游依赖：无（底层实现）
 *
 * @localProcessing 本地处理职责
 * - 🟢 API请求参数构建和验证
 * - 🟢 错误处理和重试机制
 * - 🟢 响应数据解析和格式化
 * - 🟢 请求缓存和去重
 *
 * @remoteProcessing 远程处理职责（核心功能）
 * - 🔴 调用Gemini API进行文本解析
 * - 🔴 调用Gemini Vision API进行图像分析
 * - 🔴 调用Gemini API进行多订单检测和分割
 * - 🔴 处理API响应和错误
 *
 * @compatibility 兼容性保证
 * - 保持现有API调用接口不变
 * - 兼容现有的错误处理机制
 * - 保持响应数据格式一致
 *
 * @refactoringConstraints 重构约束
 * - ✅ 专注于API调用，不处理业务逻辑
 * - ✅ 不能依赖其他子层
 * - ✅ 必须保持API调用的稳定性
 * - ✅ 保持现有的重试和错误处理机制
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-08-09
 * @lastModified 2025-08-09
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * Gemini API调用器 - 子层实现
     */
    class GeminiCaller {
        constructor() {
            this.logger = this.getLogger();
            
            // API配置
            this.config = {
                apiKey: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s', // 个人项目API密钥
                modelVersion: 'gemini-2.5-flash',
                timeout: 30000,
                maxRetries: 3,
                retryDelay: 1000
            };

            // 构建API URL
            this.baseURL = `https://generativelanguage.googleapis.com/v1beta/models/${this.config.modelVersion}:generateContent`;
            
            // 请求缓存
            this.requestCache = new Map();
            this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
            
            this.logger.log('Gemini API调用器已初始化', 'info', { 
                model: this.config.modelVersion 
            });
        }

        /**
         * 调用API - 统一入口
         * @param {string} prompt - 提示词
         * @param {string} type - 调用类型 ('text' | 'image')
         * @param {object} options - 调用选项
         * @returns {Promise<object>} API响应结果
         */
        async callAPI(prompt, type = 'text', options = {}) {
            try {
                this.logger.log('开始调用Gemini API', 'info', { 
                    type, 
                    promptLength: prompt.length,
                    options 
                });

                // 检查缓存
                const cacheKey = this.generateCacheKey(prompt, type, options);
                const cachedResult = this.getCachedResult(cacheKey);
                if (cachedResult) {
                    this.logger.log('使用缓存结果', 'info', { cacheKey });
                    return cachedResult;
                }

                let result;
                if (type === 'image') {
                    result = await this.callVisionAPI(prompt, options);
                } else {
                    result = await this.callTextAPI(prompt, options);
                }

                // 缓存结果
                this.setCachedResult(cacheKey, result);

                this.logger.log('Gemini API调用成功', 'success', { 
                    type,
                    hasResult: !!result 
                });

                return result;

            } catch (error) {
                this.logger.log('Gemini API调用失败', 'error', { 
                    type,
                    error: error.message 
                });
                throw error;
            }
        }

        /**
         * 调用文本API
         * @param {string} prompt - 文本提示词
         * @param {object} options - 选项
         * @returns {Promise<object>} API响应
         */
        async callTextAPI(prompt, options = {}) {
            const requestBody = {
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.3,
                    topK: 40,
                    topP: 0.95,
                    maxOutputTokens: 8192,
                    responseMimeType: "application/json"
                }
            };

            return await this.makeAPIRequest(requestBody, options);
        }

        /**
         * 调用Vision API
         * @param {string} base64Image - Base64图片数据
         * @param {object} options - 选项
         * @returns {Promise<object>} API响应
         */
        async callVisionAPI(base64Image, options = {}) {
            // 构建Vision API请求
            const requestBody = {
                contents: [{
                    parts: [
                        {
                            text: "请分析这张图片中的订单信息，并按照JSON格式返回结构化数据。"
                        },
                        {
                            inline_data: {
                                mime_type: "image/jpeg",
                                data: base64Image.replace(/^data:image\/[a-z]+;base64,/, '')
                            }
                        }
                    ]
                }],
                generationConfig: {
                    temperature: 0.3,
                    topK: 40,
                    topP: 0.95,
                    maxOutputTokens: 8192,
                    responseMimeType: "application/json"
                }
            };

            return await this.makeAPIRequest(requestBody, options);
        }

        /**
         * 执行API请求
         * @param {object} requestBody - 请求体
         * @param {object} options - 选项
         * @returns {Promise<object>} API响应
         */
        async makeAPIRequest(requestBody, options = {}) {
            let lastError;
            
            for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
                try {
                    const response = await fetch(`${this.baseURL}?key=${this.config.apiKey}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(requestBody),
                        signal: AbortSignal.timeout(this.config.timeout)
                    });

                    if (!response.ok) {
                        const errorData = await response.json().catch(() => ({}));
                        throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
                    }

                    const data = await response.json();
                    return this.parseAPIResponse(data);

                } catch (error) {
                    lastError = error;
                    this.logger.log(`API请求失败 (尝试 ${attempt}/${this.config.maxRetries})`, 'warning', { 
                        error: error.message 
                    });

                    if (attempt < this.config.maxRetries) {
                        await this.delay(this.config.retryDelay * attempt);
                    }
                }
            }

            throw new Error(`API请求最终失败: ${lastError.message}`);
        }

        /**
         * 解析API响应
         * @param {object} data - API响应数据
         * @returns {object} 解析后的结果
         */
        parseAPIResponse(data) {
            try {
                if (!data.candidates || data.candidates.length === 0) {
                    throw new Error('API响应中没有候选结果');
                }

                const candidate = data.candidates[0];
                if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
                    throw new Error('API响应格式不正确');
                }

                const textContent = candidate.content.parts[0].text;
                
                // 尝试解析JSON
                try {
                    return JSON.parse(textContent);
                } catch (jsonError) {
                    // 如果不是JSON格式，返回原始文本
                    return { text: textContent, rawResponse: true };
                }

            } catch (error) {
                this.logger.log('API响应解析失败', 'error', { error: error.message });
                throw new Error(`响应解析失败: ${error.message}`);
            }
        }

        /**
         * 生成缓存键
         * @param {string} prompt - 提示词
         * @param {string} type - 类型
         * @param {object} options - 选项
         * @returns {string} 缓存键
         */
        generateCacheKey(prompt, type, options) {
            const key = `${type}_${this.hashString(prompt)}_${JSON.stringify(options)}`;
            return key;
        }

        /**
         * 获取缓存结果
         * @param {string} cacheKey - 缓存键
         * @returns {object|null} 缓存结果
         */
        getCachedResult(cacheKey) {
            const cached = this.requestCache.get(cacheKey);
            if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.result;
            }
            return null;
        }

        /**
         * 设置缓存结果
         * @param {string} cacheKey - 缓存键
         * @param {object} result - 结果
         */
        setCachedResult(cacheKey, result) {
            this.requestCache.set(cacheKey, {
                result,
                timestamp: Date.now()
            });

            // 清理过期缓存
            this.cleanExpiredCache();
        }

        /**
         * 清理过期缓存
         */
        cleanExpiredCache() {
            const now = Date.now();
            for (const [key, cached] of this.requestCache.entries()) {
                if (now - cached.timestamp >= this.cacheTimeout) {
                    this.requestCache.delete(key);
                }
            }
        }

        /**
         * 字符串哈希
         * @param {string} str - 字符串
         * @returns {string} 哈希值
         */
        hashString(str) {
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // 转换为32位整数
            }
            return hash.toString(36);
        }

        /**
         * 延迟函数
         * @param {number} ms - 延迟毫秒数
         * @returns {Promise} Promise对象
         */
        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        /**
         * 获取API状态
         * @returns {object} API状态信息
         */
        getAPIStatus() {
            return {
                model: this.config.modelVersion,
                cacheSize: this.requestCache.size,
                baseURL: this.baseURL,
                timeout: this.config.timeout,
                maxRetries: this.config.maxRetries
            };
        }

        /**
         * 获取日志服务
         */
        getLogger() {
            return window.OTA?.logger || window.logger || console;
        }
    }

    // 创建全局实例
    const geminiCaller = new GeminiCaller();

    // 导出到全局作用域
    window.GeminiCaller = GeminiCaller;
    window.OTA.GeminiCaller = GeminiCaller;
    window.OTA.geminiCaller = geminiCaller;

    console.log('✅ GeminiCaller (子层实现) 已加载');

})();
