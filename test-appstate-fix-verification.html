<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AppState警告修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .status.success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .status.warning {
            background-color: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .status.info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        .console-output {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .error-count {
            font-weight: bold;
            font-size: 18px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 AppState降级警告修复验证</h1>
        
        <div id="testStatus">
            <div class="status info">
                <strong>测试状态：</strong>正在初始化...
            </div>
        </div>

        <div>
            <h3>警告统计</h3>
            <div id="warningStats">
                <div>AppState降级警告次数: <span id="warningCount" class="error-count">0</span></div>
                <div>最后警告时间: <span id="lastWarningTime">无</span></div>
            </div>
        </div>

        <div>
            <h3>控制台输出监控</h3>
            <button onclick="clearConsoleOutput()">清空输出</button>
            <button onclick="startMonitoring()">开始监控</button>
            <button onclick="stopMonitoring()">停止监控</button>
            <div id="consoleOutput" class="console-output">等待控制台输出...</div>
        </div>

        <div>
            <h3>系统状态检查</h3>
            <div id="systemStatus"></div>
        </div>

        <div>
            <h3>手动测试</h3>
            <button onclick="testAppStateAccess()">测试AppState访问</button>
            <button onclick="testMultiOrderAdapter()">测试MultiOrderAdapter</button>
            <div id="testResults"></div>
        </div>
    </div>

    <!-- 按照正确的顺序加载脚本 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/core/application-bootstrap.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/adapters/multi-order-manager-adapter.js"></script>

    <script>
        let warningCount = 0;
        let lastWarningTime = null;
        let monitoringInterval = null;
        let consoleOutput = [];

        // 拦截console.warn来监控AppState降级警告
        const originalConsoleWarn = console.warn;
        console.warn = function(...args) {
            const message = args.join(' ');
            if (message.includes('appState') && message.includes('降级获取方式')) {
                warningCount++;
                lastWarningTime = new Date().toLocaleTimeString();
                updateWarningStats();
                addConsoleOutput(`⚠️ [${lastWarningTime}] ${message}`, 'warning');
            }
            originalConsoleWarn.apply(console, args);
        };

        // 拦截console.log来监控修复信息
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            const message = args.join(' ');
            if (message.includes('MultiOrderManagerAdapter') || message.includes('AppState')) {
                const timestamp = new Date().toLocaleTimeString();
                if (message.includes('减法修复') || message.includes('延迟获取')) {
                    addConsoleOutput(`✅ [${timestamp}] ${message}`, 'success');
                } else {
                    addConsoleOutput(`📝 [${timestamp}] ${message}`, 'log');
                }
            }
            originalConsoleLog.apply(console, args);
        };

        function updateWarningStats() {
            document.getElementById('warningCount').textContent = warningCount;
            document.getElementById('lastWarningTime').textContent = lastWarningTime || '无';
            
            const statusDiv = document.getElementById('testStatus');
            if (warningCount === 0) {
                statusDiv.innerHTML = '<div class="status success"><strong>✅ 修复成功：</strong>未检测到AppState降级警告</div>';
            } else if (warningCount < 3) {
                statusDiv.innerHTML = '<div class="status warning"><strong>⚠️ 部分修复：</strong>警告次数已减少但仍存在</div>';
            } else {
                statusDiv.innerHTML = '<div class="status error"><strong>❌ 修复失败：</strong>警告仍在出现</div>';
            }
        }

        function addConsoleOutput(message, type = 'log') {
            consoleOutput.push({ message, type, timestamp: Date.now() });
            if (consoleOutput.length > 100) {
                consoleOutput.shift();
            }
            updateConsoleDisplay();
        }

        function updateConsoleDisplay() {
            const outputDiv = document.getElementById('consoleOutput');
            const html = consoleOutput.map(item => {
                const color = {
                    'warning': '#ffc107',
                    'success': '#51cf66',
                    'info': '#74c0fc',
                    'log': '#d4d4d4'
                }[item.type] || '#d4d4d4';
                return `<div style="color: ${color}">${item.message}</div>`;
            }).join('');
            outputDiv.innerHTML = html || '等待控制台输出...';
            outputDiv.scrollTop = outputDiv.scrollHeight;
        }

        function clearConsoleOutput() {
            consoleOutput = [];
            updateConsoleDisplay();
        }

        function startMonitoring() {
            if (monitoringInterval) return;
            
            monitoringInterval = setInterval(() => {
                checkSystemStatus();
            }, 2000);
            
            addConsoleOutput('🔍 开始监控系统状态...', 'info');
        }

        function stopMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
                addConsoleOutput('⏹️ 停止监控系统状态', 'info');
            }
        }

        function checkSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            const checks = [
                {
                    name: '依赖容器',
                    check: () => window.OTA && window.OTA.container
                },
                {
                    name: '服务定位器',
                    check: () => window.OTA && window.OTA.serviceLocator
                },
                {
                    name: 'AppState类',
                    check: () => window.AppState
                },
                {
                    name: 'AppState实例',
                    check: () => window.appState || window.OTA?.appState
                },
                {
                    name: 'getAppState函数',
                    check: () => typeof window.getAppState === 'function'
                },
                {
                    name: 'MultiOrderManagerAdapter',
                    check: () => window.OTA?.MultiOrderManagerAdapter
                }
            ];

            let statusHtml = '';
            checks.forEach(({ name, check }) => {
                const isOk = check();
                statusHtml += `<div class="status ${isOk ? 'success' : 'error'}">
                    ${isOk ? '✅' : '❌'} ${name}: ${isOk ? '正常' : '未找到'}
                </div>`;
            });

            statusDiv.innerHTML = statusHtml;
        }

        function testAppStateAccess() {
            const resultsDiv = document.getElementById('testResults');
            try {
                const appState = window.getAppState();
                const isLoggedIn = appState.get('auth.isLoggedIn');
                resultsDiv.innerHTML = `<div class="status success">✅ AppState访问成功，登录状态: ${isLoggedIn}</div>`;
                addConsoleOutput('✅ 手动测试：AppState访问成功', 'success');
            } catch (error) {
                resultsDiv.innerHTML = `<div class="status error">❌ AppState访问失败: ${error.message}</div>`;
                addConsoleOutput(`❌ 手动测试：AppState访问失败 - ${error.message}`, 'warning');
            }
        }

        function testMultiOrderAdapter() {
            const resultsDiv = document.getElementById('testResults');
            try {
                if (window.OTA?.MultiOrderManagerAdapter) {
                    const adapter = new window.OTA.MultiOrderManagerAdapter();
                    resultsDiv.innerHTML = `<div class="status success">✅ MultiOrderManagerAdapter创建成功</div>`;
                    addConsoleOutput('✅ 手动测试：MultiOrderManagerAdapter创建成功', 'success');
                } else {
                    resultsDiv.innerHTML = `<div class="status warning">⚠️ MultiOrderManagerAdapter类未找到</div>`;
                    addConsoleOutput('⚠️ 手动测试：MultiOrderManagerAdapter类未找到', 'warning');
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="status error">❌ MultiOrderManagerAdapter创建失败: ${error.message}</div>`;
                addConsoleOutput(`❌ 手动测试：MultiOrderManagerAdapter创建失败 - ${error.message}`, 'warning');
            }
        }

        // 初始化
        setTimeout(() => {
            updateWarningStats();
            checkSystemStatus();
            startMonitoring();
            addConsoleOutput('🚀 AppState修复验证测试已启动', 'info');
        }, 1000);

        // 5秒后检查修复效果
        setTimeout(() => {
            if (warningCount === 0) {
                addConsoleOutput('🎉 修复验证成功：5秒内未出现AppState降级警告', 'success');
            } else {
                addConsoleOutput(`⚠️ 修复验证：5秒内出现 ${warningCount} 次警告`, 'warning');
            }
        }, 5000);
    </script>
</body>
</html>
