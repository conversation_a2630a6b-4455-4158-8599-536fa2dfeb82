<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能渠道检测与自动分析测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        textarea {
            width: 100%;
            height: 120px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            resize: vertical;
        }
        select, input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .log-area {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .form-fields {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .field-group {
            display: flex;
            flex-direction: column;
        }
        .field-group label {
            font-weight: bold;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 智能渠道检测与自动分析测试</h1>
        <p>测试新实现的智能渠道检测与策略联动机制</p>
    </div>

    <div class="container">
        <h2>📋 系统状态检查</h2>
        <button onclick="checkSystemStatus()">检查系统状态</button>
        <div id="systemStatus" class="log-area"></div>
    </div>

    <div class="container">
        <h2>🎯 渠道检测测试</h2>
        <div class="test-section">
            <h3>Fliggy渠道测试</h3>
            <textarea id="fliggyTestInput" placeholder="输入包含Fliggy特征的订单内容...">
订单编号1234567890123456789
客户姓名：张先生
上车地点：吉隆坡国际机场
目的地：双子塔
商家实收：150.00
车型：舒适型五座
            </textarea>
            <button onclick="testChannelDetection('fliggy')">测试Fliggy检测</button>
        </div>

        <div class="test-section">
            <h3>JingGe渠道测试</h3>
            <textarea id="jinggeTestInput" placeholder="输入包含JingGe特征的订单内容...">
商铺订单
客户：李女士
接送：KLIA2机场到市中心
价格：120.00
车型：经济型七座
            </textarea>
            <button onclick="testChannelDetection('jingge')">测试JingGe检测</button>
        </div>

        <div class="test-section">
            <h3>通用内容测试</h3>
            <textarea id="genericTestInput" placeholder="输入不包含特定渠道特征的内容...">
客户：王先生
从：机场
到：酒店
价格：100.00
            </textarea>
            <button onclick="testChannelDetection('generic')">测试通用检测</button>
        </div>
    </div>

    <div class="container">
        <h2>🔄 自动分析测试</h2>
        <div class="test-section">
            <h3>完整自动化流程测试</h3>
            <textarea id="autoAnalysisInput" placeholder="输入订单内容进行完整自动分析...">
订单编号9876543210987654321
客户姓名：陈先生
联系电话：+60123456789
上车地点：吉隆坡中央车站
目的地：云顶高原
商家实收：200.00
车型：商务七座
备注：需要儿童座椅
            </textarea>
            <button onclick="testAutoAnalysis()">测试完整自动分析</button>
        </div>
    </div>

    <div class="container">
        <h2>📊 模拟表单字段</h2>
        <div class="form-fields">
            <div class="field-group">
                <label for="otaChannel">OTA渠道：</label>
                <select id="otaChannel">
                    <option value="">请选择OTA渠道</option>
                </select>
            </div>
            <div class="field-group">
                <label for="customerName">客户姓名：</label>
                <input type="text" id="customerName" placeholder="客户姓名">
            </div>
            <div class="field-group">
                <label for="pickup">上车地点：</label>
                <input type="text" id="pickup" placeholder="上车地点">
            </div>
            <div class="field-group">
                <label for="dropoff">目的地：</label>
                <input type="text" id="dropoff" placeholder="目的地">
            </div>
            <div class="field-group">
                <label for="contactPhone">联系电话：</label>
                <input type="text" id="contactPhone" placeholder="联系电话">
            </div>
            <div class="field-group">
                <label for="extraRequirement">额外要求：</label>
                <input type="text" id="extraRequirement" placeholder="额外要求">
            </div>
        </div>
    </div>

    <div class="container">
        <h2>📝 测试日志</h2>
        <button onclick="clearLog()">清空日志</button>
        <div id="testLog" class="log-area"></div>
    </div>

    <!-- 加载必要的脚本文件 -->
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/ota-channel-mapping.js"></script>
    <script src="js/strategies/fliggy-ota-strategy.js"></script>
    <script src="js/strategies/jingge-ota-strategy.js"></script>
    <script src="js/flow/channel-detector.js"></script>
    <script src="js/flow/prompt-builder.js"></script>
    <script src="js/controllers/business-flow-controller.js"></script>
    <script src="js/core/language-detector.js"></script>
    <script src="js/managers/form-manager.js"></script>
    <script src="test-channel-detection-simple.js"></script>

    <script>
        // 全局测试状态
        let testResults = [];

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;

            const logArea = document.getElementById('testLog');
            const logDiv = document.createElement('div');
            logDiv.className = `status ${type}`;
            logDiv.textContent = logEntry;
            logArea.appendChild(logDiv);
            logArea.scrollTop = logArea.scrollHeight;

            console.log(logEntry);
        }

        // 清空日志
        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
            testResults = [];
        }

        // 检查系统状态
        function checkSystemStatus() {
            const statusArea = document.getElementById('systemStatus');
            statusArea.innerHTML = '';

            const checks = [
                { name: 'OTA命名空间', check: () => typeof window.OTA !== 'undefined' },
                { name: '渠道映射', check: () => window.OTA?.otaChannelMapping?.commonChannels?.length > 0 },
                { name: 'Fliggy策略', check: () => typeof window.FliggyOTAStrategy !== 'undefined' },
                { name: 'JingGe策略', check: () => typeof window.JingGeOTAStrategy !== 'undefined' },
                { name: '渠道检测器', check: () => window.OTA?.ChannelDetector || window.ChannelDetector },
                { name: '提示词构建器', check: () => window.OTA?.PromptBuilder || window.PromptBuilder },
                { name: '业务流程控制器', check: () => window.OTA?.businessFlowController },
                { name: '语言检测器', check: () => window.OTA?.unifiedLanguageDetector }
            ];

            checks.forEach(({ name, check }) => {
                const result = check();
                const status = result ? '✅' : '❌';
                const div = document.createElement('div');
                div.className = result ? 'status success' : 'status error';
                div.textContent = `${status} ${name}: ${result ? '可用' : '不可用'}`;
                statusArea.appendChild(div);
            });

            // 初始化OTA渠道下拉选项
            initializeOTAChannelOptions();
        }

        // 初始化OTA渠道选项
        function initializeOTAChannelOptions() {
            const otaSelect = document.getElementById('otaChannel');
            if (window.OTA?.otaChannelMapping?.commonChannels) {
                otaSelect.innerHTML = '<option value="">请选择OTA渠道</option>';
                window.OTA.otaChannelMapping.commonChannels.forEach(channel => {
                    const option = document.createElement('option');
                    option.value = channel.value;
                    option.textContent = channel.text;
                    otaSelect.appendChild(option);
                });
                log('OTA渠道选项已初始化', 'success');
            }
        }

        // 测试渠道检测
        async function testChannelDetection(type) {
            let inputId, testName;

            switch(type) {
                case 'fliggy':
                    inputId = 'fliggyTestInput';
                    testName = 'Fliggy渠道检测';
                    break;
                case 'jingge':
                    inputId = 'jinggeTestInput';
                    testName = 'JingGe渠道检测';
                    break;
                case 'generic':
                    inputId = 'genericTestInput';
                    testName = '通用内容检测';
                    break;
                default:
                    log('未知的测试类型', 'error');
                    return;
            }

            const input = document.getElementById(inputId).value;
            if (!input.trim()) {
                log('请输入测试内容', 'warning');
                return;
            }

            log(`开始${testName}...`, 'info');

            try {
                // 测试渠道检测器
                const detector = window.OTA?.channelDetector ||
                                new (window.OTA?.ChannelDetector || window.ChannelDetector)();

                const result = await detector.detectChannel(input);

                log(`检测结果: ${JSON.stringify(result, null, 2)}`, 'info');

                if (result.channel) {
                    log(`✅ 检测到渠道: ${result.channel} (置信度: ${result.confidence})`, 'success');

                    // 测试策略获取
                    const promptBuilder = window.OTA?.promptBuilder ||
                                        new (window.OTA?.PromptBuilder || window.PromptBuilder)();
                    const strategy = promptBuilder.getChannelStrategy(result.channel);

                    if (strategy) {
                        log(`✅ 找到对应策略: ${strategy.getChannelName()}`, 'success');
                        const snippets = strategy.getFieldPromptSnippets();
                        log(`策略片段: ${Object.keys(snippets).join(', ')}`, 'info');
                    } else {
                        log(`⚠️ 未找到对应策略`, 'warning');
                    }
                } else {
                    log(`ℹ️ 未检测到特定渠道`, 'info');
                }

            } catch (error) {
                log(`❌ 检测失败: ${error.message}`, 'error');
            }
        }

        // 测试完整自动分析
        async function testAutoAnalysis() {
            const input = document.getElementById('autoAnalysisInput').value;
            if (!input.trim()) {
                log('请输入测试内容', 'warning');
                return;
            }

            log('开始完整自动分析测试...', 'info');

            try {
                // 模拟语言检测器的自动分析流程
                const languageDetector = window.OTA?.unifiedLanguageDetector;
                if (!languageDetector) {
                    log('❌ 语言检测器不可用', 'error');
                    return;
                }

                // 启用渠道检测（模拟通用用户）
                const commonChannels = window.OTA?.otaChannelMapping?.commonChannels || [];
                languageDetector.initChannelDetection(commonChannels);
                languageDetector.enableAutoAnalysis(true);

                // 触发检测和分析
                await languageDetector.detectAndApplyChannelWithAnalysis(input, 'test-field');

                log('✅ 自动分析流程已触发', 'success');

            } catch (error) {
                log(`❌ 自动分析失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            log('测试页面已加载', 'info');

            // 延迟检查系统状态，确保所有脚本都已加载
            setTimeout(() => {
                checkSystemStatus();
            }, 1000);

            // 监听自动分析完成事件
            document.addEventListener('ota-auto-analysis-completed', (event) => {
                log('🎯 收到自动分析完成事件', 'success');
                log(`分析结果: ${JSON.stringify(event.detail, null, 2)}`, 'info');
            });

            document.addEventListener('business-flow-auto-analysis-completed', (event) => {
                log('🚀 收到业务流程自动分析完成事件', 'success');
                log(`业务流程结果: ${JSON.stringify(event.detail, null, 2)}`, 'info');
            });
        });
    </script>
</body>
</html>
