/**
 * ============================================================================
 * 🚀 核心业务流程控制器 - 母层架构
 * ============================================================================
 *
 * @fileoverview 核心业务流程控制器 - 母层架构
 * @description 统一管理整个订单处理业务流程，协调各个子层模块
 * 
 * @businessFlow 核心业务流程控制
 * 完整业务流程：
 * 1. 接收输入内容（文字/图片）
 * 2. 调用本地渠道特征检测 (flow/channel-detector.js)
 * 3. 组合渠道专属提示词 (flow/prompt-builder.js)
 * 4. 调用Gemini API进行智能解析 (flow/gemini-caller.js)
 * 5. 处理解析结果（单订单/多订单分支） (flow/result-processor.js)
 * 6. 委托给订单管理控制器进行后续处理
 * 
 * @architecture Mother Layer (母层)
 * - 职责：业务流程的统一控制和协调
 * - 原则：不包含具体实现逻辑，只负责调用和协调子层
 * - 接口：提供统一的对外API接口
 * 
 * @dependencies 依赖关系
 * 上游依赖：无（作为顶层控制器）
 * 下游依赖：
 * - flow/channel-detector.js (渠道检测子层)
 * - flow/prompt-builder.js (提示词构建子层)
 * - flow/gemini-caller.js (Gemini API调用子层)
 * - flow/result-processor.js (结果处理子层)
 * - controllers/order-management-controller.js (订单管理母层)
 * 
 * @localProcessing 本地处理职责
 * - 业务流程控制和协调
 * - 子层模块的调用管理
 * - 错误处理和异常管理
 * - 状态管理和进度跟踪
 * 
 * @remoteProcessing 远程处理职责
 * - 通过子层调用Gemini API
 * - 不直接进行远程调用
 * 
 * @compatibility 兼容性保证
 * - 保持现有window.OTA.geminiService接口
 * - 保持现有parseOrder()、analyzeImage()方法签名
 * - 提供向后兼容的适配器
 * 
 * @refactoringConstraints 重构约束
 * - 必须保持现有渠道策略文件不变
 * - 严格遵循母子两层架构原则
 * - 子层不能反向依赖母层
 * - 保持单向依赖关系
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-08-09
 * @lastModified 2025-08-09
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 核心业务流程控制器 - 母层
     */
    class BusinessFlowController {
        constructor() {
            this.logger = this.getLogger();
            this.channelDetector = null;
            this.promptBuilder = null;
            this.geminiCaller = null;
            this.resultProcessor = null;
            
            // 初始化子层模块
            this.initializeChildLayers();
            
            this.logger.log('业务流程控制器已初始化', 'info');
        }

        /**
         * 初始化子层模块
         */
        initializeChildLayers() {
            try {
                // 优先使用已创建的全局单例实例（由各自文件或启动器创建）
                this.channelDetector = window.OTA?.channelDetector || null;
                this.promptBuilder = window.OTA?.promptBuilder || null;
                this.geminiCaller = window.OTA?.geminiCaller || null;
                this.resultProcessor = window.OTA?.resultProcessor || null;

                // 如仅存在类定义但未有实例，则即时创建实例并同步回写到全局，避免后续空引用
                if (!this.channelDetector) {
                    const DetectorCtor = window.OTA?.ChannelDetector || window.ChannelDetector;
                    if (typeof DetectorCtor === 'function') {
                        this.channelDetector = new DetectorCtor();
                        window.OTA = window.OTA || {};
                        window.OTA.channelDetector = this.channelDetector;
                    }
                }

                if (!this.promptBuilder) {
                    const PromptCtor = window.OTA?.PromptBuilder || window.PromptBuilder;
                    if (typeof PromptCtor === 'function') {
                        this.promptBuilder = new PromptCtor();
                        window.OTA = window.OTA || {};
                        window.OTA.promptBuilder = this.promptBuilder;
                    }
                }

                if (!this.geminiCaller) {
                    const GeminiCallerCtor = window.OTA?.GeminiCaller || window.GeminiCaller;
                    if (typeof GeminiCallerCtor === 'function') {
                        this.geminiCaller = new GeminiCallerCtor();
                        window.OTA = window.OTA || {};
                        window.OTA.geminiCaller = this.geminiCaller;
                    }
                }

                if (!this.resultProcessor) {
                    const ResultProcessorCtor = window.OTA?.ResultProcessor || window.ResultProcessor;
                    if (typeof ResultProcessorCtor === 'function') {
                        this.resultProcessor = new ResultProcessorCtor();
                        window.OTA = window.OTA || {};
                        window.OTA.resultProcessor = this.resultProcessor;
                    }
                }

                if (!this.channelDetector || !this.promptBuilder || !this.geminiCaller || !this.resultProcessor) {
                    this.logger.log('部分子层模块未加载，将使用降级方案', 'warning');
                }
            } catch (error) {
                this.logger.log('子层模块初始化失败', 'error', { error: error.message });
            }
        }

        /**
         * 主要处理入口 - 统一的业务流程控制
         * @param {string|object} input - 输入内容（文字或图片）
         * @param {string} type - 输入类型 ('text' | 'image')
         * @param {object} options - 处理选项
         * @returns {Promise<object>} 处理结果
         */
        async processInput(input, type = 'text', options = {}) {
            try {
                this.logger.log('开始处理输入', 'info', {
                    type,
                    inputLength: input?.length || 0,
                    autoTriggered: options.autoTriggered || false,
                    sourceField: options.sourceField || 'unknown'
                });

                // 步骤1：渠道检测（如果是自动触发且已有检测结果，跳过重复检测）
                let channelResult;
                if (options.autoTriggered && options.detectedChannel) {
                    channelResult = {
                        channel: options.detectedChannel,
                        confidence: options.confidence || 0.8,
                        method: 'auto_detected'
                    };
                    this.logger.log('使用自动检测的渠道结果', 'info', channelResult);
                } else {
                    channelResult = await this.detectChannel(input, options);
                }

                // 步骤2：组合渠道专属提示词
                this.logger.log('🔧 [调试] 开始构建提示词', 'debug', { 
                    channel: channelResult.channel, 
                    confidence: channelResult.confidence,
                    hasStrategy: channelResult.channel ? (channelResult.channel.toLowerCase() === 'fliggy' ? !!window.FliggyOTAStrategy : false) : false
                });
                const prompt = await this.buildPrompt(input, channelResult, options);
                this.logger.log('🔧 [调试] 提示词构建完成', 'debug', { 
                    promptLength: prompt?.length || 0,
                    containsChannelInfo: prompt ? prompt.includes(channelResult.channel || '') : false
                });

                // 步骤3：调用Gemini API进行智能解析
                const geminiResult = await this.callGeminiAPI(prompt, type, options);

                // 步骤4：处理解析结果
                const processedResult = await this.processResult(geminiResult, channelResult, options);

                // 步骤5：如果是自动触发，进行额外的处理
                if (options.autoTriggered) {
                    await this.handleAutoTriggeredResult(processedResult, options);
                }

                this.logger.log('输入处理完成', 'success', {
                    channel: channelResult.channel,
                    resultType: processedResult.type,
                    autoTriggered: options.autoTriggered || false
                });

                return processedResult;

            } catch (error) {
                this.logger.log('输入处理失败', 'error', {
                    error: error.message,
                    autoTriggered: options.autoTriggered || false
                });
                throw error;
            }
        }

        /**
         * 本地渠道特征检测
         * @param {string} input - 输入内容
         * @param {object} options - 选项
         * @returns {Promise<object>} 检测结果
         */
        async detectChannel(input, options = {}) {
            try {
                if (this.channelDetector) {
                    return await this.channelDetector.detectChannel(input, options);
                } else {
                    // 降级方案：使用现有的检测器
                    const detector = window.OTA.OTAChannelDetector;
                    if (detector) {
                        return await detector.detectChannel(input, null, options);
                    }
                    return { channel: null, confidence: 0 };
                }
            } catch (error) {
                this.logger.log('渠道检测失败', 'error', { error: error.message });
                return { channel: null, confidence: 0 };
            }
        }

        /**
         * 组合渠道专属提示词
         * @param {string} input - 输入内容
         * @param {object} channelResult - 渠道检测结果
         * @param {object} options - 选项
         * @returns {Promise<string>} 组合后的提示词
         */
        async buildPrompt(input, channelResult, options = {}) {
            try {
                if (this.promptBuilder) {
                    return await this.promptBuilder.buildPrompt(input, channelResult, options);
                } else {
                    // 降级方案：使用现有的提示词生成逻辑
                    return this.buildPromptFallback(input, channelResult, options);
                }
            } catch (error) {
                this.logger.log('提示词构建失败', 'error', { error: error.message });
                return input; // 最简单的降级方案
            }
        }

        /**
         * 调用Gemini API
         * @param {string} prompt - 提示词
         * @param {string} type - 输入类型
         * @param {object} options - 选项
         * @returns {Promise<object>} API结果
         */
        async callGeminiAPI(prompt, type, options = {}) {
            try {
                if (this.geminiCaller) {
                    return await this.geminiCaller.callAPI(prompt, type, options);
                } else {
                    // 降级方案：使用现有的Gemini服务
                    const geminiService = this.getGeminiService();
                    if (type === 'image') {
                        return await geminiService.analyzeImage(prompt);
                    } else {
                        return await geminiService.parseOrder(prompt, options.isRealtime || false);
                    }
                }
            } catch (error) {
                this.logger.log('Gemini API调用失败', 'error', { error: error.message });
                throw error;
            }
        }

        /**
         * 处理解析结果
         * @param {object} geminiResult - Gemini解析结果
         * @param {object} channelResult - 渠道检测结果
         * @param {object} options - 选项
         * @returns {Promise<object>} 处理结果
         */
        async processResult(geminiResult, channelResult, options = {}) {
            try {
                if (this.resultProcessor) {
                    return await this.resultProcessor.processResult(geminiResult, channelResult, options);
                } else {
                    // 降级方案：简单的结果处理
                    return this.processResultFallback(geminiResult, channelResult, options);
                }
            } catch (error) {
                this.logger.log('结果处理失败', 'error', { error: error.message });
                throw error;
            }
        }

        // ========================================
        // 向后兼容的API接口
        // ========================================

        /**
         * 解析订单文本 - 兼容现有接口
         * @param {string} text - 订单文本
         * @param {boolean} isRealtime - 是否实时解析
         * @returns {Promise<object>} 解析结果
         */
        async parseOrder(text, isRealtime = false) {
            return await this.processInput(text, 'text', { isRealtime });
        }

        /**
         * 分析图片 - 兼容现有接口
         * @param {string} base64Image - Base64图片数据
         * @returns {Promise<object>} 分析结果
         */
        async analyzeImage(base64Image) {
            return await this.processInput(base64Image, 'image');
        }

        /**
         * 检测和分割多订单 - 兼容现有接口
         * @param {string} text - 订单文本
         * @param {object} options - 选项
         * @returns {Promise<object>} 检测结果
         */
        async detectAndSplitMultiOrdersWithVerification(text, options = {}) {
            const result = await this.processInput(text, 'text', { ...options, detectMultiOrder: true });
            return {
                isMultiOrder: result.type === 'multi-order',
                orders: result.orders || [result.order],
                confidence: result.confidence || 0.8
            };
        }

        // ========================================
        // 降级方案和工具方法
        // ========================================

        /**
         * 提示词构建降级方案
         */
        buildPromptFallback(input, channelResult, options = {}) {
            // 简单的提示词构建逻辑
            let prompt = input;
            if (channelResult.channel) {
                prompt = `渠道：${channelResult.channel}\n\n${input}`;
            }
            return prompt;
        }

        /**
         * 结果处理降级方案
         */
        processResultFallback(geminiResult, channelResult, options = {}) {
            // 简单的结果处理逻辑
            const isMultiOrder = geminiResult.isMultiOrder || 
                                (geminiResult.orders && Array.isArray(geminiResult.orders) && geminiResult.orders.length > 1);
            
            return {
                type: isMultiOrder ? 'multi-order' : 'single-order',
                order: isMultiOrder ? null : geminiResult,
                orders: isMultiOrder ? geminiResult.orders : [geminiResult],
                channel: channelResult.channel,
                confidence: geminiResult.confidence || 0.8
            };
        }

        /**
         * 获取日志服务
         */
        getLogger() {
            return window.OTA?.logger || window.logger || console;
        }

        /**
         * 处理自动触发的结果
         * @param {object} processedResult - 处理结果
         * @param {object} options - 选项
         */
        async handleAutoTriggeredResult(processedResult, options) {
            try {
                this.logger.log('处理自动触发结果', 'info', {
                    resultType: processedResult.type,
                    sourceField: options.sourceField,
                    detectedChannel: options.detectedChannel
                });

                // 1. 更新UI状态指示
                this.updateAutoAnalysisStatus('completed', {
                    channel: options.detectedChannel,
                    confidence: options.confidence
                });

                // 2. 如果是单订单结果，可以进行进一步处理
                if (processedResult.type === 'single-order' && processedResult.order) {
                    // 可以在这里添加自动填充表单等逻辑
                    this.logger.log('单订单自动分析完成', 'success', {
                        customerName: processedResult.order.customerName,
                        pickup: processedResult.order.pickup,
                        dropoff: processedResult.order.dropoff
                    });
                }

                // 3. 触发自定义事件
                this.dispatchAutoAnalysisCompletedEvent(processedResult, options);

            } catch (error) {
                this.logger.log('自动触发结果处理失败', 'error', error);
            }
        }

        /**
         * 更新自动分析状态
         * @param {string} status - 状态
         * @param {object} data - 状态数据
         */
        updateAutoAnalysisStatus(status, data = {}) {
            try {
                this.logger.log(`自动分析状态: ${status}`, 'info', data);

                // 可以在这里更新UI状态指示器
                // 例如显示"分析完成"的提示
            } catch (error) {
                this.logger.log('更新自动分析状态失败', 'error', error);
            }
        }

        /**
         * 触发自动分析完成事件
         * @param {object} processedResult - 处理结果
         * @param {object} options - 选项
         */
        dispatchAutoAnalysisCompletedEvent(processedResult, options) {
            try {
                const event = new CustomEvent('business-flow-auto-analysis-completed', {
                    detail: {
                        result: processedResult,
                        options,
                        timestamp: Date.now()
                    }
                });
                document.dispatchEvent(event);
                this.logger.log('自动分析完成事件已触发', 'info');
            } catch (error) {
                this.logger.log('触发自动分析完成事件失败', 'error', error);
            }
        }

        /**
         * 获取Gemini服务
         */
        getGeminiService() {
            return window.OTA?.geminiService || window.geminiService || window.getGeminiService?.();
        }
    }

    // 创建全局实例
    const businessFlowController = new BusinessFlowController();

    // 导出到全局作用域
    window.BusinessFlowController = BusinessFlowController;
    window.OTA.BusinessFlowController = BusinessFlowController;
    window.OTA.businessFlowController = businessFlowController;

    // 向后兼容：替换现有的geminiService
    window.OTA.geminiService = businessFlowController;
    window.geminiService = businessFlowController;

    console.log('✅ BusinessFlowController (母层控制器) 已加载');

})();
