// Script Manifest - authoritative list of scripts and phases
// Keep order strictly as required by architecture. See .github/copilot-instructions.md

window.OTA = window.OTA || {};

(function() {
  'use strict';

  // Simple environment switch: use window.OTA.env or URL ?dev=1 to include dev tools
  const isDev = (() => {
    try {
      const fromOTA = window.OTA && window.OTA.env && (window.OTA.env === 'dev' || window.OTA.env === 'development');
      const urlDev = /[?&]dev=1(?:&|$)/.test(location.search);
      return !!(fromOTA || urlDev);
    } catch (_) {
      return false;
    }
  })();

  // Define phases with ordered scripts. Avoid external origins (CSP)
  const phases = [
    { name: 'core', scripts: [
      'js/core/dependency-container.js',
      'js/core/service-locator.js',
      'js/core/ota-registry.js',
      'js/core/application-bootstrap.js',
      'js/core/global-event-coordinator.js',
      'js/core/component-lifecycle-manager.js',
      'js/core/unified-data-manager.js',
      'js/core/vehicle-configuration-manager.js',
      'js/core/vehicle-config-integration.js',
      'js/core/global-field-standardization-layer.js',
      'js/core/language-detector.js',
      'js/core/base-ota-strategy.js',
      'js/core/ota-configuration-manager.js',
      'js/core/ota-event-bridge.js',
      'js/core/ota-system-integrator.js',
      'js/adapters/base-manager-adapter.js',
      'js/adapters/ota-manager-decorator.js',
      'js/core/ota-manager-factory.js',
      'js/core/feature-toggle.js',
      'js/core/ota-bootstrap-integration.js',
    ] },
    { name: 'base-utils', scripts: [
      'js/utils.js',
      'js/logger.js',
      'js/ota-channel-mapping.js',
      'js/hotel-name-database.js',
      'js/hotel-data-complete.js'  // 🔧 CORS修复：完整酒店数据，在知识库之前加载
    ] },
    // ota-system阶段已移除 - 相关功能已集成到新架构中
    { name: 'strategies', scripts: [
      'js/strategies/fliggy-ota-strategy.js',
      'js/strategies/jingge-ota-strategy.js'
    ] },
    { name: 'new-architecture', scripts: [
      // Flow子层 (7个文件) - 业务流程实现
      'js/flow/channel-detector.js',
      'js/flow/prompt-builder.js',
      'js/flow/gemini-caller.js',
      'js/flow/result-processor.js',
      'js/flow/order-parser.js',
      'js/flow/knowledge-base.js',
      'js/flow/address-translator.js',

      // Order子层 (3个文件) - 订单处理实现
      'js/order/multi-order-handler.js',
      'js/order/api-caller.js',
      'js/order/history-manager.js',

      // 母层控制器 (2个文件) - 核心控制逻辑
      'js/controllers/business-flow-controller.js',
      'js/controllers/order-management-controller.js',

      // 适配器层 (3个文件) - 兼容性保证
      'js/adapters/gemini-service-adapter.js',
      'js/adapters/multi-order-manager-adapter.js',
      'js/adapters/ui-manager-adapter.js'
    ] },
    { name: 'services', scripts: [
      'js/app-state.js',
      'js/language-manager.js',
      'js/api-service.js',
      'js/hotel-data-inline.js',
      // 'js/gemini-service.js', // ⚠️ 已迁移到新架构 - 通过适配器保持兼容性
      'js/order-history-manager.js',
      'js/image-upload-manager.js',
  'js/currency-converter.js',
  'js/flight-info-service.js',
  'js/address-translation-service.js'
    ] },
    { name: 'multi-order', scripts: [
      'js/multi-order/field-mapping-config.js',
      'js/multi-order/field-mapping-validator.js',
      'js/multi-order/multi-order-detector.js',
      'js/multi-order/multi-order-renderer.js',
      'js/multi-order/multi-order-processor.js',
      'js/multi-order/multi-order-transformer.js',
      'js/multi-order/field-mapping-tests.js',
      'js/multi-order/multi-order-state-manager.js',
      'js/multi-order/batch-processor.js',
      'js/multi-order/multi-order-coordinator.js',
      // 'js/multi-order-manager-v2.js', // ⚠️ 已迁移到新架构 - 通过适配器保持兼容性
      'js/multi-order/system-integrity-checker.js'
    ] },
    { name: 'ui-deps', scripts: [
      'js/paging-service-manager.js',
      'js/grid-resizer.js',
      'js/i18n.js',
      'js/auto-resize-manager.js',
      'js/managers/form-manager.js',
      'js/managers/price-manager.js',
      'js/managers/event-manager.js',
      'js/managers/ui-state-manager.js',
  'js/managers/ota-manager.js',
  'js/managers/realtime-analysis-manager.js'
    ] },
    { name: 'ui', scripts: [
      'js/ui-manager.js',
      'main.js'
    ] }
  ];

  window.OTA.scriptManifest = {
    phases,
    version: '1.0',
    createdAt: new Date().toISOString()
  };

  console.log('✅ Script manifest ready');
})();
