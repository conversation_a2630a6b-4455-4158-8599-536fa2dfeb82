<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复验证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .status.success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .status.warning {
            background-color: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .status.info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        .console-output {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .error-count {
            font-weight: bold;
            font-size: 18px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 realtimeAnalysisManager 错误修复验证</h1>
        
        <div id="testStatus">
            <div class="status info">
                <strong>测试状态：</strong>正在初始化...
            </div>
        </div>

        <div>
            <h3>错误统计</h3>
            <div id="errorStats">
                <div>realtimeAnalysisManager 错误次数: <span id="errorCount" class="error-count">0</span></div>
                <div>最后错误时间: <span id="lastErrorTime">无</span></div>
            </div>
        </div>

        <div>
            <h3>控制台输出监控</h3>
            <button onclick="clearConsoleOutput()">清空输出</button>
            <button onclick="startMonitoring()">开始监控</button>
            <button onclick="stopMonitoring()">停止监控</button>
            <div id="consoleOutput" class="console-output">等待控制台输出...</div>
        </div>

        <div>
            <h3>系统状态检查</h3>
            <div id="systemStatus"></div>
        </div>
    </div>

    <!-- 加载核心依赖 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/core/feature-toggle.js"></script>
    <script src="js/core/global-field-standardization-layer.js"></script>

    <script>
        let errorCount = 0;
        let lastErrorTime = null;
        let monitoringInterval = null;
        let consoleOutput = [];

        // 拦截console.error来监控错误
        const originalConsoleError = console.error;
        console.error = function(...args) {
            const message = args.join(' ');
            if (message.includes('realtimeAnalysisManager') && message.includes('未找到')) {
                errorCount++;
                lastErrorTime = new Date().toLocaleTimeString();
                updateErrorStats();
                addConsoleOutput(`❌ [${lastErrorTime}] ${message}`, 'error');
            }
            originalConsoleError.apply(console, args);
        };

        // 拦截console.log来监控修复信息
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            const message = args.join(' ');
            if (message.includes('GlobalFieldStandardization')) {
                const timestamp = new Date().toLocaleTimeString();
                if (message.includes('减法修复') || message.includes('跳过实时分析')) {
                    addConsoleOutput(`✅ [${timestamp}] ${message}`, 'success');
                } else if (message.includes('实时分析管理器未找到')) {
                    addConsoleOutput(`ℹ️ [${timestamp}] ${message}`, 'info');
                } else {
                    addConsoleOutput(`📝 [${timestamp}] ${message}`, 'log');
                }
            }
            originalConsoleLog.apply(console, args);
        };

        function updateErrorStats() {
            document.getElementById('errorCount').textContent = errorCount;
            document.getElementById('lastErrorTime').textContent = lastErrorTime || '无';
            
            const statusDiv = document.getElementById('testStatus');
            if (errorCount === 0) {
                statusDiv.innerHTML = '<div class="status success"><strong>✅ 修复成功：</strong>未检测到 realtimeAnalysisManager 错误</div>';
            } else if (errorCount < 5) {
                statusDiv.innerHTML = '<div class="status warning"><strong>⚠️ 部分修复：</strong>错误次数已减少但仍存在</div>';
            } else {
                statusDiv.innerHTML = '<div class="status error"><strong>❌ 修复失败：</strong>错误仍在大量出现</div>';
            }
        }

        function addConsoleOutput(message, type = 'log') {
            consoleOutput.push({ message, type, timestamp: Date.now() });
            if (consoleOutput.length > 100) {
                consoleOutput.shift(); // 保持最新100条
            }
            updateConsoleDisplay();
        }

        function updateConsoleDisplay() {
            const outputDiv = document.getElementById('consoleOutput');
            const html = consoleOutput.map(item => {
                const color = {
                    'error': '#ff6b6b',
                    'success': '#51cf66',
                    'info': '#74c0fc',
                    'log': '#d4d4d4'
                }[item.type] || '#d4d4d4';
                return `<div style="color: ${color}">${item.message}</div>`;
            }).join('');
            outputDiv.innerHTML = html || '等待控制台输出...';
            outputDiv.scrollTop = outputDiv.scrollHeight;
        }

        function clearConsoleOutput() {
            consoleOutput = [];
            updateConsoleDisplay();
        }

        function startMonitoring() {
            if (monitoringInterval) return;
            
            monitoringInterval = setInterval(() => {
                checkSystemStatus();
            }, 2000);
            
            addConsoleOutput('🔍 开始监控系统状态...', 'info');
        }

        function stopMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
                addConsoleOutput('⏹️ 停止监控系统状态', 'info');
            }
        }

        function checkSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            const checks = [
                {
                    name: '依赖容器',
                    check: () => window.OTA && window.OTA.container
                },
                {
                    name: '服务定位器',
                    check: () => window.OTA && window.OTA.serviceLocator
                },
                {
                    name: '特性开关',
                    check: () => window.OTA && window.OTA.featureToggle
                },
                {
                    name: '字段标准化层',
                    check: () => window.OTA && window.OTA.globalFieldStandardizationLayer
                },
                {
                    name: '字段标准化已初始化',
                    check: () => window.OTA?.globalFieldStandardizationLayer?.initialized
                },
                {
                    name: 'realtimeAnalysisManager (全局)',
                    check: () => window.realtimeAnalysisManager
                }
            ];

            let statusHtml = '';
            checks.forEach(({ name, check }) => {
                const isOk = check();
                statusHtml += `<div class="status ${isOk ? 'success' : 'error'}">
                    ${isOk ? '✅' : '❌'} ${name}: ${isOk ? '正常' : '未找到'}
                </div>`;
            });

            statusDiv.innerHTML = statusHtml;
        }

        // 初始化
        setTimeout(() => {
            updateErrorStats();
            checkSystemStatus();
            startMonitoring();
            addConsoleOutput('🚀 修复验证测试已启动', 'info');
        }, 1000);

        // 5秒后检查修复效果
        setTimeout(() => {
            if (errorCount === 0) {
                addConsoleOutput('🎉 修复验证成功：5秒内未出现 realtimeAnalysisManager 错误', 'success');
            } else {
                addConsoleOutput(`⚠️ 修复验证：5秒内出现 ${errorCount} 次错误`, 'warning');
            }
        }, 5000);
    </script>
</body>
</html>
