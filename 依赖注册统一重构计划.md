依赖注入统一化详细转移计划                        │ │
│ │                                                   │ │
│ │ 项目概述                                          │ │
│ │                                                   │ │
│ │ 技术栈                                            │ │
│ │                                                   │ │
│ │ - 前端框架: Vanilla JavaScript + 模块化架构       │ │
│ │ - 依赖管理: DependencyContainer (目标统一系统)    │ │
│ │ - 当前混乱状态: 3套并行系统 (DependencyContainer  │ │
│ │ + ServiceLocator + 全局变量)                      │ │
│ │ - 构建工具: 无 (纯前端HTML+JS)                    │ │
│ │                                                   │ │
│ │ API设计目标                                       │ │
│ │                                                   │ │
│ │ // 统一API：唯一的服务访问方式                    │ │
│ │ const service = container.get('serviceName');     │ │
│ │                                                   │ │
│ │ // 替代当前的5种混乱方式：                        │ │
│ │ // window.OTA.serviceName                         │ │
│ │ // window.serviceName                             │ │
│ │ // window.getServiceName()                        │ │
│ │ // window.OTA.getService('serviceName')           │ │
│ │ // ServiceLocator.getService('serviceName')       │ │
│ │                                                   │ │
│ │ 开发目标                                          │ │
│ │                                                   │ │
│ │ 1. 架构统一: 单一依赖注入系统                     │ │
│ │ 2. 代码简化: 消除1030个混乱引用点                 │ │
│ │ 3. 维护性: 清晰的依赖关系图                       │ │
│ │ 4. 性能优化: 消除多重查找路径                     │ │
│ │                                                   │ │
│ │ 最终目的                                          │ │
│ │                                                   │ │
│ │ 建立现代化、可维护的依赖注入架构，为项目长期发展  │ │
│ │ 奠定基础。                                        │ │
│ │                                                   │ │
│ │ 详细实施计划                                      │ │
│ │                                                   │ │
│ │ 阶段1：基础设施建立 (1周)                         │ │
│ │                                                   │ │
│ │ 目标                                              │ │
│ │                                                   │ │
│ │ 建立统一服务访问的基础设施                        │ │
│ │                                                   │ │
│ │ 涉及文件                                          │ │
│ │                                                   │ │
│ │ - js/core/dependency-container.js (增强)          │ │
│ │ - js/core/service-registry.js (新建)              │ │
│ │ - js/core/migration-helpers.js (新建)             │ │
│ │                                                   │ │
│ │ 任务清单                                          │ │
│ │                                                   │ │
│ │ 1. 增强DependencyContainer功能                    │ │
│ │   - 添加服务别名支持                              │ │
│ │   - 实现延迟加载机制                              │ │
│ │   - 增加服务健康检查                              │ │
│ │ 2. 创建统一访问函数                               │ │
│ │   - 实现 getService(name) 全局函数                │ │
│ │   - 建立服务名称映射表                            │ │
│ │   - 添加向后兼容支持                              │ │
│ │ 3. 建立服务注册表                                 │ │
│ │   - 67个需要迁移的文件清单                        │ │
│ │   - 26个直接构造的服务清单                        │ │
│ │   - 服务依赖关系图                                │ │
│ │                                                   │ │
│ │ 验证标准                                          │ │
│ │                                                   │ │
│ │ - DependencyContainer API测试通过                 │ │
│ │ - getService()函数可访问所有现有服务              │ │
│ │ - 现有功能100%正常运行                            │ │
│ │                                                   │ │
│ │ ---                                               │ │
│ │ 阶段2：核心模块迁移 (2周)                         │ │
│ │                                                   │ │
│ │ 目标                                              │ │
│ │                                                   │ │
│ │ 迁移core模块到DependencyContainer                 │ │
│ │                                                   │ │
│ │ 涉及文件 (13个核心文件)                           │ │
│ │                                                   │ │
│ │ js/core/application-bootstrap.js      ✓           │ │
│ │ 已部分使用DependencyContainer                     │ │
│ │ js/core/service-locator.js           ❌ 需要废弃   │ │
│ │ js/core/dependency-container.js      ✓ 保留增强   │ │
│ │ js/core/feature-toggle.js           🔄 需要迁移   │ │
│ │ js/core/ota-manager-factory.js      🔄 需要迁移   │ │
│ │ js/core/script-loader.js            🔄 需要迁移   │ │
│ │ js/core/ota-registry.js             🔄 需要迁移   │ │
│ │ js/core/ota-system-integrator.js    🔄 需要迁移   │ │
│ │ js/core/global-event-coordinator.js 🔄 需要迁移   │ │
│ │ js/core/language-detector.js        🔄 需要迁移   │ │
│ │ js/core/vehicle-configuration-manager.js 🔄       │ │
│ │ 需要迁移                                          │ │
│ │ js/core/unified-data-manager.js     🔄 需要迁移   │ │
│ │ js/core/component-lifecycle-manager.js 🔄         │ │
│ │ 需要迁移                                          │ │
│ │                                                   │ │
│ │ 任务清单                                          │ │
│ │                                                   │ │
│ │ 1. application-bootstrap.js 增强                  │ │
│ │   - 移除ServiceLocator依赖                        │ │
│ │   - 纯DependencyContainer实现                     │ │
│ │   - 优化服务注册流程                              │ │
│ │ 2. 逐文件迁移模式                                 │ │
│ │ // 旧代码模式                                     │ │
│ │ const logger = window.OTA.logger ||               │ │
│ │ window.getLogger();                               │ │
│ │                                                   │ │
│ │ // 新代码模式                                     │ │
│ │ const logger = container.get('logger');           │ │
│ │ 3. 依赖关系重建                                   │ │
│ │   - 识别每个服务的真实依赖                        │ │
│ │   - 在DependencyContainer中正确注册               │ │
│ │   - 消除循环依赖                                  │ │
│ │                                                   │ │
│ │ 验证标准                                          │ │
│ │                                                   │ │
│ │ - 核心模块完全通过DependencyContainer运行         │ │
│ │ - 应用启动流程正常                                │ │
│ │ - 所有核心服务功能验证通过                        │ │
│ │                                                   │ │
│ │ ---                                               │ │
│ │ 阶段3：业务模块迁移 (2周)                         │ │
│ │                                                   │ │
│ │ 目标                                              │ │
│ │                                                   │ │
│ │ 迁移managers、controllers、flow等业务模块         │ │
│ │                                                   │ │
│ │ 涉及文件分组                                      │ │
│ │                                                   │ │
│ │ 3.1 Managers模块 (6个文件)                        │ │
│ │ js/managers/event-manager.js         🔄 高优先级  │ │
│ │ js/managers/form-manager.js          🔄 中优先级  │ │
│ │ js/managers/realtime-analysis-manager.js 🔄       │ │
│ │ 高优先级                                          │ │
│ │ js/managers/ui-state-manager.js      🔄 中优先级  │ │
│ │ js/managers/ota-manager.js           🔄 低优先级  │ │
│ │ js/managers/price-manager.js         🔄 低优先级  │ │
│ │                                                   │ │
│ │ 3.2 Controllers模块 (2个文件)                     │ │
│ │ js/controllers/business-flow-controller.js 🔄     │ │
│ │ 高优先级                                          │ │
│ │ js/controllers/order-management-controller.js 🔄  │ │
│ │ 高优先级                                          │ │
│ │                                                   │ │
│ │ 3.3 Flow模块 (7个文件)                            │ │
│ │ js/flow/channel-detector.js          🔄 高优先级  │ │
│ │ js/flow/gemini-caller.js             🔄 高优先级  │ │
│ │ js/flow/prompt-builder.js            🔄 中优先级  │ │
│ │ js/flow/result-processor.js          🔄 中优先级  │ │
│ │ js/flow/order-parser.js              🔄 中优先级  │ │
│ │ js/flow/knowledge-base.js            🔄 低优先级  │ │
│ │ js/flow/address-translator.js        🔄 低优先级  │ │
│ │                                                   │ │
│ │ 任务清单                                          │ │
│ │                                                   │ │
│ │ 1. 按优先级分批迁移                               │ │
│ │   - 高优先级: 影响核心业务流程                    │ │
│ │   - 中优先级: 影响用户体验                        │ │
│ │   - 低优先级: 辅助功能                            │ │
│ │ 2. 服务工厂函数重写                               │ │
│ │ // 在DependencyContainer中注册                    │ │
│ │ container.register('eventManager', () => {        │ │
│ │     return new EventManager(                      │ │
│ │         container.get('logger'),                  │ │
│ │         container.get('appState')                 │ │
│ │     );                                            │ │
│ │ });                                               │ │
│ │ 3. 接口兼容性保证                                 │ │
│ │   - 保持所有对外API不变                           │ │
│ │   - 内部实现切换到DependencyContainer             │ │
│ │                                                   │ │
│ │ 验证标准                                          │ │
│ │                                                   │ │
│ │ - 每个模块迁移后完整功能测试                      │ │
│ │ - 业务流程端到端验证                              │ │
│ │ - 性能测试无回归                                  │ │
│ │                                                   │ │
│ │ ---                                               │ │
│ │ 阶段4：Multi-Order系统迁移 (2周)                  │ │
│ │                                                   │ │
│ │ 目标                                              │ │
│ │                                                   │ │
│ │ 迁移复杂的多订单处理系统                          │ │
│ │                                                   │ │
│ │ 涉及文件 (11个文件)                               │ │
│ │                                                   │ │
│ │ js/multi-order/multi-order-coordinator.js    🔄   │ │
│ │ 核心协调器                                        │ │
│ │ js/multi-order/multi-order-detector.js       🔄   │ │
│ │ 检测服务                                          │ │
│ │ js/multi-order/multi-order-processor.js      🔄   │ │
│ │ 处理服务                                          │ │
│ │ js/multi-order/multi-order-renderer.js       🔄   │ │
│ │ 渲染服务                                          │ │
│ │ js/multi-order/multi-order-transformer.js    🔄   │ │
│ │ 数据转换                                          │ │
│ │ js/multi-order/batch-processor.js            🔄   │ │
│ │ 批处理器                                          │ │
│ │ js/multi-order/field-mapping-config.js       🔄   │ │
│ │ 配置管理                                          │ │
│ │ js/multi-order/field-mapping-validator.js    🔄   │ │
│ │ 验证器                                            │ │
│ │ js/multi-order/multi-order-state-manager.js  🔄   │ │
│ │ 状态管理                                          │ │
│ │ js/multi-order/system-integrity-checker.js   🔄   │ │
│ │ 完整性检查                                        │ │
│ │ js/multi-order/field-mapping-tests.js        🔄   │ │
│ │ 测试套件                                          │ │
│ │                                                   │ │
│ │ 任务清单                                          │ │
│ │                                                   │ │
│ │ 1. 依赖链分析                                     │ │
│ │   - MultiOrder系统内部依赖关系复杂                │ │
│ │   - 需要仔细分析避免循环依赖                      │ │
│ │   - 建立正确的初始化顺序                          │ │
│ │ 2. 批量服务注册                                   │ │
│ │ const multiOrderServices = [                      │ │
│ │     'multiOrderCoordinator',                      │ │
│ │     'multiOrderDetector',                         │ │
│ │     'multiOrderProcessor',                        │ │
│ │     'multiOrderRenderer',                         │ │
│ │     // ... 其他服务                               │ │
│ │ ];                                                │ │
│ │                                                   │ │
│ │ multiOrderServices.forEach(serviceName => {       │ │
│ │     container.register(serviceName,               │ │
│ │ createFactory(serviceName));                      │ │
│ │ });                                               │ │
│ │ 3. 状态管理重构                                   │ │
│ │   -                                               │ │
│ │ MultiOrderStateManager整合到DependencyContainer   │ │
│ │   - 状态同步机制优化                              │ │
│ │                                                   │ │
│ │ 验证标准                                          │ │
│ │                                                   │ │
│ │ - 多订单处理完整流程测试                          │ │
│ │ - 字段映射功能验证                                │ │
│ │ - 批处理性能测试                                  │ │
│ │                                                   │ │
│ │ ---                                               │ │
│ │ 阶段5：剩余模块迁移 (1.5周)                       │ │
│ │                                                   │ │
│ │ 目标                                              │ │
│ │                                                   │ │
│ │ 迁移所有剩余模块                                  │ │
│ │                                                   │ │
│ │ 涉及文件分组 (31个文件)                           │ │
│ │                                                   │ │
│ │ 5.1 服务层 (8个文件)                              │ │
│ │ js/api-service.js                    🔄 API服务   │ │
│ │ js/address-translation-service.js    🔄 地址翻译  │ │
│ │ js/flight-info-service.js           🔄 航班信息   │ │
│ │ js/image-upload-manager.js          🔄 图片上传   │ │
│ │ js/language-manager.js              🔄 语言管理   │ │
│ │ js/order-history-manager.js         🔄 订单历史   │ │
│ │ js/paging-service-manager.js        🔄 分页服务   │ │
│ │ js/currency-converter.js            🔄 货币转换   │ │
│ │                                                   │ │
│ │ 5.2 适配器层 (5个文件)                            │ │
│ │ js/adapters/base-manager-adapter.js     🔄        │ │
│ │ 基础适配器                                        │ │
│ │ js/adapters/gemini-service-adapter.js   🔄        │ │
│ │ Gemini适配器                                      │ │
│ │ js/adapters/multi-order-manager-adapter.js 🔄     │ │
│ │ 多订单适配器                                      │ │
│ │ js/adapters/ota-manager-decorator.js    🔄        │ │
│ │ OTA装饰器                                         │ │
│ │ js/adapters/ui-manager-adapter.js       🔄        │ │
│ │ UI适配器                                          │ │
│ │                                                   │ │
│ │ 5.3 Order子系统 (3个文件)                         │ │
│ │ js/order/api-caller.js               🔄 API调用器 │ │
│ │ js/order/history-manager.js          🔄 历史管理  │ │
│ │ js/order/multi-order-handler.js      🔄           │ │
│ │ 多订单处理                                        │ │
│ │                                                   │ │
│ │ 5.4 工具和配置 (15个文件)                         │ │
│ │ js/app-state.js, js/logger.js, js/utils.js,       │ │
│ │ js/i18n.js                                        │ │
│ │ js/ui-manager.js, js/auto-resize-manager.js,      │ │
│ │ js/grid-resizer.js                                │ │
│ │ js/hotel-data-complete.js,                        │ │
│ │ js/hotel-data-inline.js                           │ │
│ │ js/hotel-name-database.js,                        │ │
│ │ js/ota-channel-mapping.js                         │ │
│ │ js/strategies/fliggy-ota-strategy.js              │ │
│ │ js/strategies/jingge-ota-strategy.js              │ │
│ │ js/core/global-field-standardization-layer.js     │ │
│ │ js/core/ota-bootstrap-integration.js              │ │
│ │                                                   │ │
│ │ 任务清单                                          │ │
│ │                                                   │ │
│ │ 1. 批量迁移工具                                   │ │
│ │   - 开发自动化脚本辅助迁移                        │ │
│ │   - 代码模式识别和替换                            │ │
│ │ 2. 适配器层重构                                   │ │
│ │   - 适配器模式到依赖注入的转换                    │ │
│ │   - 装饰器模式优化                                │ │
│ │ 3. 工具类整合                                     │ │
│ │   - 公共工具类的依赖注入改造                      │ │
│ │   - 配置管理统一化                                │ │
│ │                                                   │ │
│ │ 验证标准                                          │ │
│ │                                                   │ │
│ │ - 全模块功能完整性测试                            │ │
│ │ - 端到端业务流程验证                              │ │
│ │ - 性能基准测试                                    │ │
│ │                                                   │ │
│ │ ---                                               │ │
│ │ 阶段6：清理和优化 (1周)                           │ │
│ │                                                   │ │
│ │ 目标                                              │ │
│ │                                                   │ │
│ │ 清理旧代码，优化性能                              │ │
│ │                                                   │ │
│ │ 任务清单                                          │ │
│ │                                                   │ │
│ │ 1. 废弃代码清理                                   │ │
│ │ // 删除这些文件/代码段                            │ │
│ │ - js/core/service-locator.js (整个文件)           │ │
│ │ - 所有 window.getXxxManager() 函数                │ │
│ │ - 所有 window.OTA.getService() 调用               │ │
│ │ - 所有 window.xxxManager 全局变量                 │ │
│ │ 2. DependencyContainer优化                        │ │
│ │   - 服务注册性能优化                              │ │
│ │   - 循环依赖检测优化                              │ │
│ │   - 内存使用优化                                  │ │
│ │ 3. 代码质量提升                                   │ │
│ │   - ESLint规则更新                                │ │
│ │   - 依赖注入最佳实践文档                          │ │
│ │   - 代码审查清单                                  │ │
│ │                                                   │ │
│ │ 验证标准                                          │ │
│ │                                                   │ │
│ │ - 代码覆盖率测试                                  │ │
│ │ - 性能回归测试                                    │ │
│ │ - 内存泄漏检测                                    │ │
│ │                                                   │ │
│ │ ---                                               │ │
│ │ 详细TODO清单                                      │ │
│ │                                                   │ │
│ │ 准备阶段 TODOs                                    │ │
│ │                                                   │ │
│ │ - 分析67个需要迁移的文件，建立迁移优先级表        │ │
│ │ - 创建DependencyContainer增强功能设计文档         │ │
│ │ - 建立服务依赖关系图谱                            │ │
│ │ - 准备完整的回滚计划和备份策略                    │ │
│ │ - 建立自动化测试套件确保功能完整性                │ │
│ │                                                   │ │
│ │ 阶段1 TODOs (基础设施)                            │ │
│ │                                                   │ │
│ │ - 增强DependencyContainer: 添加服务别名支持       │ │
│ │ - 增强DependencyContainer: 实现延迟加载机制       │ │
│ │ - 增强DependencyContainer: 添加服务健康检查       │ │
│ │ - 创建统一的 getService(name) 全局函数            │ │
│ │ - 建立服务名称映射表 (67个服务)                   │ │
│ │ - 创建 js/core/service-registry.js 服务注册中心   │ │
│ │ - 创建 js/core/migration-helpers.js 迁移辅助工具  │ │
│ │ - 编写DependencyContainer API完整测试套件         │ │
│ │ - 验证getService()可访问所有现有服务              │ │
│ │ - 确认现有功能100%正常运行                        │ │
│ │                                                   │ │
│ │ 阶段2 TODOs (核心模块)                            │ │
│ │                                                   │ │
│ │ - 重构 application-bootstrap.js                   │ │
│ │ 移除ServiceLocator依赖                            │ │
│ │ - 迁移 feature-toggle.js 到DependencyContainer    │ │
│ │ - 迁移 ota-manager-factory.js                     │ │
│ │ 到DependencyContainer                             │ │
│ │ - 迁移 script-loader.js 到DependencyContainer     │ │
│ │ - 迁移 ota-registry.js 到DependencyContainer      │ │
│ │ - 迁移 ota-system-integrator.js                   │ │
│ │ 到DependencyContainer                             │ │
│ │ - 迁移 global-event-coordinator.js                │ │
│ │ 到DependencyContainer                             │ │
│ │ - 迁移 language-detector.js 到DependencyContainer │ │
│ │ - 迁移 vehicle-configuration-manager.js           │ │
│ │ 到DependencyContainer                             │ │
│ │ - 迁移 unified-data-manager.js                    │ │
│ │ 到DependencyContainer                             │ │
│ │ - 迁移 component-lifecycle-manager.js             │ │
│ │ 到DependencyContainer                             │ │
│ │ - 验证核心模块完全通过DependencyContainer运行     │ │
│ │ - 验证应用启动流程正常                            │ │
│ │ - 验证所有核心服务功能                            │ │
│ │                                                   │ │
│ │ 阶段3 TODOs (业务模块)                            │ │
│ │                                                   │ │
│ │ - 迁移Managers模块: event-manager.js (高优先级)   │ │
│ │ - 迁移Managers模块: realtime-analysis-manager.js  │ │
│ │ (高优先级)                                        │ │
│ │ - 迁移Managers模块: form-manager.js (中优先级)    │ │
│ │ - 迁移Managers模块: ui-state-manager.js           │ │
│ │ (中优先级)                                        │ │
│ │ - 迁移Managers模块: ota-manager.js (低优先级)     │ │
│ │ - 迁移Managers模块: price-manager.js (低优先级)   │ │
│ │ - 迁移Controllers模块:                            │ │
│ │ business-flow-controller.js                       │ │
│ │ - 迁移Controllers模块:                            │ │
│ │ order-management-controller.js                    │ │
│ │ - 迁移Flow模块: channel-detector.js (高优先级)    │ │
│ │ - 迁移Flow模块: gemini-caller.js (高优先级)       │ │
│ │ - 迁移Flow模块: prompt-builder.js (中优先级)      │ │
│ │ - 迁移Flow模块: result-processor.js (中优先级)    │ │
│ │ - 迁移Flow模块: order-parser.js (中优先级)        │ │
│ │ - 迁移Flow模块: knowledge-base.js (低优先级)      │ │
│ │ - 迁移Flow模块: address-translator.js (低优先级)  │ │
│ │ - 验证每个模块迁移后完整功能                      │ │
│ │ - 验证业务流程端到端                              │ │
│ │ - 执行性能测试无回归                              │ │
│ │                                                   │ │
│ │ 阶段4 TODOs (Multi-Order系统)                     │ │
│ │                                                   │ │
│ │ - 分析MultiOrder系统内部依赖关系                  │ │
│ │ - 建立MultiOrder服务正确初始化顺序                │ │
│ │ - 迁移 multi-order-coordinator.js (核心协调器)    │ │
│ │ - 迁移 multi-order-detector.js (检测服务)         │ │
│ │ - 迁移 multi-order-processor.js (处理服务)        │ │
│ │ - 迁移 multi-order-renderer.js (渲染服务)         │ │
│ │ - 迁移 multi-order-transformer.js (数据转换)      │ │
│ │ - 迁移 batch-processor.js (批处理器)              │ │
│ │ - 迁移 field-mapping-config.js (配置管理)         │ │
│ │ - 迁移 field-mapping-validator.js (验证器)        │ │
│ │ - 迁移 multi-order-state-manager.js (状态管理)    │ │
│ │ - 迁移 system-integrity-checker.js (完整性检查)   │ │
│ │ - 迁移 field-mapping-tests.js (测试套件)          │ │
│ │ - 验证多订单处理完整流程                          │ │
│ │ - 验证字段映射功能                                │ │
│ │ - 执行批处理性能测试                              │ │
│ │                                                   │ │
│ │ 阶段5 TODOs (剩余模块)                            │ │
│ │                                                   │ │
│ │ - 迁移服务层8个文件 (api-service.js等)            │ │
│ │ - 迁移适配器层5个文件 (base-manager-adapter.js等) │ │
│ │ - 迁移Order子系统3个文件 (api-caller.js等)        │ │
│ │ - 迁移工具和配置15个文件 (app-state.js等)         │ │
│ │ - 开发批量迁移辅助脚本                            │ │
│ │ - 重构适配器模式到依赖注入                        │ │
│ │ - 优化装饰器模式实现                              │ │
│ │ - 整合公共工具类依赖注入                          │ │
│ │ - 统一配置管理                                    │ │
│ │ - 验证全模块功能完整性                            │ │
│ │ - 验证端到端业务流程                              │ │
│ │ - 执行性能基准测试                                │ │
│ │                                                   │ │
│ │ 阶段6 TODOs (清理优化)                            │ │
│ │                                                   │ │
│ │ - 删除 js/core/service-locator.js 整个文件        │ │
│ │ - 删除所有 window.getXxxManager() 函数定义        │ │
│ │ - 删除所有 window.OTA.getService() 调用           │ │
│ │ - 删除所有 window.xxxManager 全局变量设置         │ │
│ │ - 优化DependencyContainer服务注册性能             │ │
│ │ - 优化循环依赖检测算法                            │ │
│ │ - 优化内存使用和垃圾回收                          │ │
│ │ - 更新ESLint规则支持依赖注入模式                  │ │
│ │ - 编写依赖注入最佳实践文档                        │ │
│ │ - 创建代码审查清单                                │ │
│ │ - 执行代码覆盖率测试                              │ │
│ │ - 执行性能回归测试                                │ │
│ │ - 执行内存泄漏检测                                │ │
│ │                                                   │ │
│ │ 验证和质量保证 TODOs                              │ │
│ │                                                   │ │
│ │ - 建立每阶段完成的功能测试清单                    │ │
│ │ - 建立性能基准测试和回归检测                      │ │
│ │ - 建立内存使用监控和泄漏检测                      │ │
│ │ - 建立依赖关系图谱可视化工具                      │ │
│ │ - 编写迁移完成后的系统架构文档                    │ │
│ │ - 建立新的开发规范和代码审查标准                  │ │
│ │                                                   │ │
│ │ 总计: 107个具体任务                               │ │
│ │ 预估总工期: 6-8周                                 │ │
│ │ 涉及文件: 67个JS文件                              │ │
│ │ 风险级别: 中等 (分阶段可控)      