/**
 * ============================================================================
 * 🚀 核心业务流程 - Fliggy渠道策略 (保持不变的策略文件)
 * ============================================================================
 *
 * @fileoverview Fliggy渠道策略 - 保持不变的策略文件
 * @description 提供Fliggy渠道专属的字段级提示词片段，纯静态工具类
 *
 * @businessFlow 渠道策略提供
 * 在核心业务流程中的作用：
 * 输入内容 → 本地渠道特征检测 → 【检测到Fliggy渠道时调用此文件】
 *     ↓
 * A2. 有渠道特征 → 【当前文件职责】调取渠道策略文件提示词片段
 *     ↓
 * 组合提示词 → 发送Gemini API → 结果处理 → 订单管理
 *
 * @architecture 独立策略层 (Strategy Layer)
 * - 职责：提供渠道专属配置和规则
 * - 原则：纯静态，无状态，无副作用
 * - 接口：静态方法，不依赖实例
 *
 * @localProcessing 本地处理职责（核心功能）
 * - 🟢 提供字段级提示词片段 (getFieldPromptSnippets)
 * - 🟢 提供价格计算规则（商家实收×0.84×0.615 马来西亚）
 * - 🟢 提供价格计算规则（商家实收×0.84×0.2 新加坡）
 * - 🟢 提供车型ID映射（经济型/舒适型/五座→5）
 * - 🟢 提供渠道名称标识 (getChannelName)
 *
 * @remoteProcessing 远程处理职责
 * - ❌ 无（纯配置文件，不调用任何API）
 *
 * @dependencies 依赖关系
 * 上游依赖：
 * - flow/prompt-builder.js (调用getFieldPromptSnippets)
 * - 价格计算模块 (调用价格规则)
 * 下游依赖：无（底层策略提供者）
 *
 * @compatibility 兼容性保证
 * - ✅ 接口完全保持不变
 * - ✅ getFieldPromptSnippets()方法签名不变
 * - ✅ 返回数据格式不变
 * - ✅ 价格计算公式不变
 * - ✅ 车型ID映射不变
 *
 * @refactoringConstraints 重构约束（严格执行）
 * - 🔒 严禁修改此文件的接口和实现
 * - 🔒 必须保持现有的策略逻辑
 * - 🔒 不能改变价格计算公式
 * - 🔒 不能改变车型ID映射
 * - 🔒 不能改变字段提示词片段内容
 *
 * @refactoringPlan 重构计划
 * - ✅ 此文件保持完全不变
 * - ✅ 只添加详细的业务流程注释
 * - ✅ 确保在新架构中正常工作
 * - ✅ 验证接口调用的兼容性
 *
 * <AUTHOR>
 * @version 1.0.0 (稳定版本，不升级)
 * @since 2024-XX-XX
 * @lastModified 2025-08-09 (仅添加注释)
 * @refactoringStatus 保持不变 - 策略文件不参与重构
 */
class FliggyOTAStrategy {

    /**
     * 获取策略的渠道名称
     * @returns {string} 渠道名称
     */
    static getChannelName() {
        return 'fliggy';
    }


    /**
     * 获取字段级提示词片段（单渠道单文件）
     * 仅做抽取/格式指引，不重复引擎中的业务处理
     * @param {{isMultiOrder?: boolean}} _ctx - 上下文参数（当前未使用，保留用于未来扩展）
     * @returns {Record<string, string>}
     */
    static getFieldPromptSnippets(_ctx = {}) {
        return {
            // 渠道名称固定返回
            ota: '渠道识别：请识别这是飞猪(Fliggy)渠道的订单，输出JSON时ota字段请设置为"Fliggy"。',
            // 价格与车型ID映射片段
            ota_price: '价格识别与换算：请识别"商家实收"数值作为订单价格；识别订单所属地区，若为马来西亚，最终价格=商家实收×0.84×0.615；若为新加坡，最终价格=商家实收×0.84×0.2；均保留两位小数，输出最终价；无法确定地区时仅输出基础价并标注原因，不要猜测。',
            car_type_id: '车型ID映射（根据中文车型名称）：请从订单文本中识别车型名称，然后按以下映射返回对应ID：经济型/舒适型/五座→5；经济型/舒适型/七座→35；商务七座/商务型七座→31；豪华七座/豪华型七座→32；商务九座/商务型九座→20；中巴/小巴→24。输出JSON时请设置car_type_id为上述ID之一的整数，若无法识别车型名称则返回null，不要猜测或输出名称。',
            // 新增：日期和地区字段强化
            pickup_date: 'Fliggy日期提取：**必须从订单文本中提取具体日期**，如"2025-08-11"等完整格式，转换为YYYY-MM-DD格式输出。',
            driving_region_id: 'Fliggy地区映射：根据地点准确识别driving_region_id，特别注意斗湖机场→4(Sabah)，不可返回null。',
            // 字段完整性确保
            pickup_location: '上车地点：必须从订单中提取完整地点名称，保持原始描述准确性。',
            dropoff_location: '下车地点：必须从订单中提取完整地点名称，保持原始描述准确性。'
        };
    }

    /**
     * 统一车型映射（名称→ID）
     * @returns {Object} 车型ID映射
     */
    static getVehicleIdMapping() {
        return {
            economy_comfort_5: 5,     // 经济/舒适 五座
            economy_comfort_7: 35,    // 经济/舒适 七座
            business_7: 31,           // 商务七座
            luxury_7: 32,             // 豪华七座
            business_9: 20,           // 商务九座
            minibus: 24               // 中巴
        };
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FliggyOTAStrategy;
} else if (typeof window !== 'undefined') {
    window.FliggyOTAStrategy = FliggyOTAStrategy;
}

console.log('✅ FliggyOTAStrategy (重构版) 已加载');
